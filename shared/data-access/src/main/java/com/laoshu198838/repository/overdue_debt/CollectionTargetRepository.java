package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.entity.overdue_debt.CollectionTarget;
import com.laoshu198838.entity.overdue_debt.CollectionTargetKey;
import com.laoshu198838.config.DataSource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 清收目标表数据访问接口
 * 
 * <p>提供对清收目标表的数据访问操作，包括基本的CRUD操作和业务查询方法。
 * 支持按年份、按公司、按目标金额等多种维度的数据查询和统计。</p>
 * 
 * <h3>主要功能：</h3>
 * <ul>
 *   <li>基本CRUD操作（继承自JpaRepository）</li>
 *   <li>按年份查询清收目标</li>
 *   <li>按管理公司查询清收目标</li>
 *   <li>清收目标统计分析</li>
 *   <li>目标金额范围查询</li>
 * </ul>
 * 
 * <h3>数据源配置：</h3>
 * <ul>
 *   <li>使用主数据源（overdue_debt_db）</li>
 *   <li>支持事务管理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 * @see CollectionTarget
 * @see CollectionTargetKey
 */
@Repository
@DataSource("primary")
public interface CollectionTargetRepository extends JpaRepository<CollectionTarget, CollectionTargetKey> {

    // ==================== 基本查询方法 ====================

    /**
     * 根据年份查询所有清收目标
     * 
     * @param year 目标年份
     * @return 指定年份的所有清收目标记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.year = :year ORDER BY ct.sequence")
    List<CollectionTarget> findByYear(@Param("year") Integer year);

    /**
     * 根据管理公司查询所有清收目标
     * 
     * @param managementCompany 管理公司名称
     * @return 指定公司的所有清收目标记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.managementCompany = :managementCompany ORDER BY ct.id.year DESC")
    List<CollectionTarget> findByManagementCompany(@Param("managementCompany") String managementCompany);

    /**
     * 根据管理公司和年份查询清收目标
     * 
     * @param managementCompany 管理公司名称
     * @param year 目标年份
     * @return 匹配的清收目标记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.managementCompany = :managementCompany AND ct.id.year = :year")
    Optional<CollectionTarget> findByManagementCompanyAndYear(@Param("managementCompany") String managementCompany, @Param("year") Integer year);

    /**
     * 查询指定年份范围内的清收目标
     * 
     * @param startYear 开始年份
     * @param endYear 结束年份
     * @return 指定年份范围内的清收目标记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.id.year BETWEEN :startYear AND :endYear ORDER BY ct.id.year, ct.sequence")
    List<CollectionTarget> findByYearRange(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);

    /**
     * 根据目标金额范围查询清收目标
     * 
     * @param minAmount 最小目标金额
     * @param maxAmount 最大目标金额
     * @return 目标金额在指定范围内的记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.targetAmount BETWEEN :minAmount AND :maxAmount ORDER BY ct.targetAmount DESC")
    List<CollectionTarget> findByTargetAmountRange(@Param("minAmount") BigDecimal minAmount, @Param("maxAmount") BigDecimal maxAmount);

    // ==================== 统计查询方法 ====================

    /**
     * 按年份统计清收目标
     * 
     * @return 年份统计结果，包含年份、公司数量、总目标金额、平均目标金额等
     */
    @Query(value = """
        SELECT 
            年份 as year,
            COUNT(*) as companyCount,
            SUM(清收目标金额) as totalAmount,
            AVG(清收目标金额) as avgAmount,
            MAX(清收目标金额) as maxAmount,
            MIN(清收目标金额) as minAmount
        FROM 清收目标表
        GROUP BY 年份
        ORDER BY 年份
        """, nativeQuery = true)
    List<Map<String, Object>> getYearlyStatistics();

    /**
     * 按管理公司统计清收目标
     * 
     * @return 公司统计结果，包含公司名称、年份数量、总目标金额、平均目标金额等
     */
    @Query(value = """
        SELECT 
            管理公司 as managementCompany,
            COUNT(*) as yearCount,
            SUM(清收目标金额) as totalAmount,
            AVG(清收目标金额) as avgAmount,
            MAX(清收目标金额) as maxAmount,
            MIN(清收目标金额) as minAmount
        FROM 清收目标表
        GROUP BY 管理公司
        ORDER BY SUM(清收目标金额) DESC
        """, nativeQuery = true)
    List<Map<String, Object>> getCompanyStatistics();

    /**
     * 获取指定年份的清收目标排名
     * 
     * @param year 目标年份
     * @return 按目标金额降序排列的公司排名
     */
    @Query(value = """
        SELECT 
            管理公司 as managementCompany,
            清收目标金额 as targetAmount,
            RANK() OVER (ORDER BY 清收目标金额 DESC) as ranking
        FROM 清收目标表
        WHERE 年份 = :year
        ORDER BY 清收目标金额 DESC
        """, nativeQuery = true)
    List<Map<String, Object>> getYearlyRanking(@Param("year") Integer year);

    /**
     * 获取总体统计信息
     * 
     * @return 包含总记录数、公司数量、年份数量、总目标金额等信息
     */
    @Query(value = """
        SELECT 
            COUNT(*) as totalRecords,
            COUNT(DISTINCT 管理公司) as companyCount,
            COUNT(DISTINCT 年份) as yearCount,
            SUM(清收目标金额) as totalAmount,
            AVG(清收目标金额) as avgAmount,
            MAX(清收目标金额) as maxAmount,
            MIN(清收目标金额) as minAmount,
            MIN(年份) as earliestYear,
            MAX(年份) as latestYear
        FROM 清收目标表
        """, nativeQuery = true)
    Map<String, Object> getOverallStatistics();

    // ==================== 业务查询方法 ====================

    /**
     * 查询所有不同的年份
     * 
     * @return 所有年份的列表，按年份升序排列
     */
    @Query("SELECT DISTINCT ct.id.year FROM CollectionTarget ct ORDER BY ct.id.year")
    List<Integer> findAllYears();

    /**
     * 查询所有不同的管理公司
     * 
     * @return 所有管理公司的列表，按公司名称排序
     */
    @Query("SELECT DISTINCT ct.id.managementCompany FROM CollectionTarget ct ORDER BY ct.id.managementCompany")
    List<String> findAllManagementCompanies();

    /**
     * 查询目标金额大于指定值的记录
     * 
     * @param amount 目标金额阈值
     * @return 目标金额大于指定值的记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.targetAmount > :amount ORDER BY ct.targetAmount DESC")
    List<CollectionTarget> findByTargetAmountGreaterThan(@Param("amount") BigDecimal amount);

    /**
     * 查询目标金额为零的记录
     * 
     * @return 目标金额为零的记录
     */
    @Query("SELECT ct FROM CollectionTarget ct WHERE ct.targetAmount = 0 ORDER BY ct.id.year, ct.sequence")
    List<CollectionTarget> findZeroTargetRecords();

    /**
     * 检查指定公司和年份的记录是否存在
     * 
     * @param managementCompany 管理公司名称
     * @param year 目标年份
     * @return 如果记录存在则返回true
     */
    boolean existsByIdManagementCompanyAndIdYear(String managementCompany, Integer year);

    /**
     * 统计指定年份的记录数量
     * 
     * @param year 目标年份
     * @return 指定年份的记录数量
     */
    @Query("SELECT COUNT(ct) FROM CollectionTarget ct WHERE ct.id.year = :year")
    long countByYear(@Param("year") Integer year);

    /**
     * 统计指定公司的记录数量
     * 
     * @param managementCompany 管理公司名称
     * @return 指定公司的记录数量
     */
    @Query("SELECT COUNT(ct) FROM CollectionTarget ct WHERE ct.id.managementCompany = :managementCompany")
    long countByManagementCompany(@Param("managementCompany") String managementCompany);
}
