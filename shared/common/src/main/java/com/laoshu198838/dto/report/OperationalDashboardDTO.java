package com.laoshu198838.dto.report;

import java.math.BigDecimal;

/**
 * 经营调度会看板数据传输对象
 * 用于逾期债权数据导出到经营调度会看板
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class OperationalDashboardDTO {
    
    /**
     * 序号
     */
    private Integer sequenceNumber;
    
    /**
     * 债权人
     */
    private String creditor;
    
    /**
     * 债务人
     */
    private String debtor;
    
    /**
     * 债权金额（万元）
     */
    private BigDecimal debtAmount;
    
    /**
     * 债权余额（万元）
     */
    private BigDecimal debtBalance;
    
    /**
     * 已处置本金（万元）
     */
    private BigDecimal disposedPrincipal;
    
    /**
     * 已处置利息（万元）
     */
    private BigDecimal disposedInterest;
    
    /**
     * 已处置违约金（万元）
     */
    private BigDecimal disposedPenalty;
    
    /**
     * 已处置其他（万元）
     */
    private BigDecimal disposedOther;
    
    /**
     * 合计处置金额（万元）
     */
    private BigDecimal totalDisposedAmount;
    
    /**
     * 处置比例
     */
    private BigDecimal disposalRatio;
    
    // 构造函数
    public OperationalDashboardDTO() {
        // 初始化BigDecimal字段为0，避免空指针
        this.debtAmount = BigDecimal.ZERO;
        this.debtBalance = BigDecimal.ZERO;
        this.disposedPrincipal = BigDecimal.ZERO;
        this.disposedInterest = BigDecimal.ZERO;
        this.disposedPenalty = BigDecimal.ZERO;
        this.disposedOther = BigDecimal.ZERO;
        this.totalDisposedAmount = BigDecimal.ZERO;
        this.disposalRatio = BigDecimal.ZERO;
    }
    
    // Getter和Setter方法
    public Integer getSequenceNumber() {
        return sequenceNumber;
    }
    
    public void setSequenceNumber(Integer sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }
    
    public String getCreditor() {
        return creditor;
    }
    
    public void setCreditor(String creditor) {
        this.creditor = creditor;
    }
    
    public String getDebtor() {
        return debtor;
    }
    
    public void setDebtor(String debtor) {
        this.debtor = debtor;
    }
    
    public BigDecimal getDebtAmount() {
        return debtAmount;
    }
    
    public void setDebtAmount(BigDecimal debtAmount) {
        this.debtAmount = debtAmount != null ? debtAmount : BigDecimal.ZERO;
    }
    
    public BigDecimal getDebtBalance() {
        return debtBalance;
    }
    
    public void setDebtBalance(BigDecimal debtBalance) {
        this.debtBalance = debtBalance != null ? debtBalance : BigDecimal.ZERO;
    }
    
    public BigDecimal getDisposedPrincipal() {
        return disposedPrincipal;
    }
    
    public void setDisposedPrincipal(BigDecimal disposedPrincipal) {
        this.disposedPrincipal = disposedPrincipal != null ? disposedPrincipal : BigDecimal.ZERO;
    }
    
    public BigDecimal getDisposedInterest() {
        return disposedInterest;
    }
    
    public void setDisposedInterest(BigDecimal disposedInterest) {
        this.disposedInterest = disposedInterest != null ? disposedInterest : BigDecimal.ZERO;
    }
    
    public BigDecimal getDisposedPenalty() {
        return disposedPenalty;
    }
    
    public void setDisposedPenalty(BigDecimal disposedPenalty) {
        this.disposedPenalty = disposedPenalty != null ? disposedPenalty : BigDecimal.ZERO;
    }
    
    public BigDecimal getDisposedOther() {
        return disposedOther;
    }
    
    public void setDisposedOther(BigDecimal disposedOther) {
        this.disposedOther = disposedOther != null ? disposedOther : BigDecimal.ZERO;
    }
    
    public BigDecimal getTotalDisposedAmount() {
        return totalDisposedAmount;
    }
    
    public void setTotalDisposedAmount(BigDecimal totalDisposedAmount) {
        this.totalDisposedAmount = totalDisposedAmount != null ? totalDisposedAmount : BigDecimal.ZERO;
    }
    
    public BigDecimal getDisposalRatio() {
        return disposalRatio;
    }
    
    public void setDisposalRatio(BigDecimal disposalRatio) {
        this.disposalRatio = disposalRatio != null ? disposalRatio : BigDecimal.ZERO;
    }
    
    /**
     * 计算合计处置金额
     * 合计 = 本金 + 利息 + 违约金 + 其他
     */
    public void calculateTotalDisposedAmount() {
        this.totalDisposedAmount = this.disposedPrincipal
                .add(this.disposedInterest)
                .add(this.disposedPenalty)
                .add(this.disposedOther);
    }
    
    /**
     * 计算处置比例
     * 处置比例 = 合计处置金额 / 债权金额 * 100%
     */
    public void calculateDisposalRatio() {
        if (this.debtAmount != null && this.debtAmount.compareTo(BigDecimal.ZERO) > 0) {
            this.disposalRatio = this.totalDisposedAmount
                    .divide(this.debtAmount, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        } else {
            this.disposalRatio = BigDecimal.ZERO;
        }
    }
    
    @Override
    public String toString() {
        return "OperationalDashboardDTO{" +
                "sequenceNumber=" + sequenceNumber +
                ", creditor='" + creditor + '\'' +
                ", debtor='" + debtor + '\'' +
                ", debtAmount=" + debtAmount +
                ", debtBalance=" + debtBalance +
                ", disposedPrincipal=" + disposedPrincipal +
                ", disposedInterest=" + disposedInterest +
                ", disposedPenalty=" + disposedPenalty +
                ", disposedOther=" + disposedOther +
                ", totalDisposedAmount=" + totalDisposedAmount +
                ", disposalRatio=" + disposalRatio +
                '}';
    }
}