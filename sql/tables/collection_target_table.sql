-- =====================================================
-- 创建清收目标表 (CollectionTarget)
-- 用于存储各公司的逾期债权年度清收目标
-- =====================================================

-- 使用overdue_debt_db数据库
USE overdue_debt_db;

-- 创建清收目标表
CREATE TABLE IF NOT EXISTS `清收目标表` (
  `管理公司` VARCHAR(50) NOT NULL COMMENT '管理公司名称',
  `年份` INT NOT NULL COMMENT '目标年份',
  `序号` INT NOT NULL COMMENT '记录序号',
  `清收目标金额` DECIMAL(19,2) NOT NULL DEFAULT 0.00 COMMENT '年度清收目标金额（万元）',
  `创建时间` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `更新时间` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `创建人` VARCHAR(50) NULL COMMENT '创建人员',
  `备注` TEXT NULL COMMENT '备注信息',
  
  -- 复合主键：管理公司 + 年份
  PRIMARY KEY (`管理公司`, `年份`),
  
  -- 索引定义
  INDEX `idx_year` (`年份`) COMMENT '年份索引',
  INDEX `idx_company` (`管理公司`) COMMENT '管理公司索引',
  INDEX `idx_create_time` (`创建时间`) COMMENT '创建时间索引'
  
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='逾期债权年度清收目标表';

-- 插入初始数据（基于Excel文件内容）
INSERT INTO `清收目标表` (`管理公司`, `年份`, `序号`, `清收目标金额`, `创建人`, `备注`) VALUES
-- 2023年数据
('亿万无线', 2023, 2, 4240.00, 'system', '从Excel导入'),
('长春万润', 2023, 4, 1264.00, 'system', '从Excel导入'),
('日上光电', 2023, 5, 121.00, 'system', '从Excel导入'),
('万润半导体', 2023, 6, 736.00, 'system', '从Excel导入'),
('万象新动', 2023, 7, 311.00, 'system', '从Excel导入'),
('恒润光电', 2023, 8, 1470.00, 'system', '从Excel导入'),
('长江万润', 2023, 9, 0.00, 'system', '从Excel导入'),

-- 2024年数据
('信立传媒', 2024, 3, 6617.00, 'system', '从Excel导入'),
('长春万润', 2024, 4, 2245.00, 'system', '从Excel导入'),
('日上光电', 2024, 5, 2267.00, 'system', '从Excel导入'),
('万润半导体', 2024, 6, 1851.00, 'system', '从Excel导入'),
('万象新动', 2024, 7, 489.00, 'system', '从Excel导入'),
('恒润光电', 2024, 8, 5529.00, 'system', '从Excel导入'),
('长江万润', 2024, 9, 58442.40, 'system', '从Excel导入'),
('万润新能源', 2024, 10, 1000.00, 'system', '从Excel导入'),
('万润科技', 2024, 11, 0.00, 'system', '从Excel导入'),
('重庆万润', 2024, 12, 0.00, 'system', '从Excel导入'),

-- 2025年数据
('中筑天佑', 2025, 1, 8700.00, 'system', '从Excel导入'),
('亿万无线', 2025, 2, 4300.00, 'system', '从Excel导入'),
('信立传媒', 2025, 3, 2500.00, 'system', '从Excel导入'),
('长春万润', 2025, 4, 2155.00, 'system', '从Excel导入'),
('日上光电', 2025, 5, 1000.00, 'system', '从Excel导入'),
('万润半导体', 2025, 6, 3175.00, 'system', '从Excel导入'),
('万象新动', 2025, 7, 490.00, 'system', '从Excel导入'),
('恒润光电', 2025, 8, 1000.00, 'system', '从Excel导入'),
('长江万润', 2025, 9, 20000.00, 'system', '从Excel导入'),
('万润新能源', 2025, 10, 1000.00, 'system', '从Excel导入'),
('万润科技', 2025, 11, 1000.00, 'system', '从Excel导入'),
('重庆万润', 2025, 12, 1000.00, 'system', '从Excel导入');

-- 验证数据插入
SELECT 
    '数据插入验证' as 操作,
    COUNT(*) as 总记录数,
    COUNT(DISTINCT `管理公司`) as 公司数量,
    COUNT(DISTINCT `年份`) as 年份数量,
    MIN(`年份`) as 最早年份,
    MAX(`年份`) as 最晚年份,
    SUM(`清收目标金额`) as 总目标金额
FROM `清收目标表`;

-- 按年份统计
SELECT 
    `年份`,
    COUNT(*) as 公司数量,
    SUM(`清收目标金额`) as 年度总目标,
    AVG(`清收目标金额`) as 平均目标,
    MAX(`清收目标金额`) as 最高目标,
    MIN(`清收目标金额`) as 最低目标
FROM `清收目标表`
GROUP BY `年份`
ORDER BY `年份`;

-- 按公司统计
SELECT 
    `管理公司`,
    COUNT(*) as 年份数量,
    SUM(`清收目标金额`) as 总目标金额,
    AVG(`清收目标金额`) as 平均年度目标
FROM `清收目标表`
GROUP BY `管理公司`
ORDER BY SUM(`清收目标金额`) DESC;

COMMIT;
