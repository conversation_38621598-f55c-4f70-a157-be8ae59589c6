#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时脚本：读取Excel文件内容
用于分析各公司债权清收目标.xlsx文件的结构
"""

import pandas as pd
import sys
import os

def read_excel_file(file_path):
    """读取Excel文件并显示内容"""
    try:
        print(f"正在读取文件: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return
        
        # 读取Excel文件
        df = pd.read_excel(file_path, engine='openpyxl')
        
        print("\n=== 文件基本信息 ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n=== 列名信息 ===")
        for i, col in enumerate(df.columns):
            print(f"{i+1}. {col}")
        
        print("\n=== 前5行数据预览 ===")
        print(df.head())
        
        print("\n=== 数据类型信息 ===")
        print(df.dtypes)
        
        print("\n=== 数据统计信息 ===")
        print(df.describe())
        
        # 检查是否有管理公司列
        company_columns = [col for col in df.columns if '公司' in str(col)]
        if company_columns:
            print(f"\n=== 包含'公司'的列: {company_columns} ===")
            for col in company_columns:
                unique_companies = df[col].dropna().unique()
                print(f"{col} 的唯一值: {list(unique_companies)}")
        
        # 检查是否有年份相关列
        year_columns = [col for col in df.columns if '年' in str(col) or 'year' in str(col).lower()]
        if year_columns:
            print(f"\n=== 包含'年'的列: {year_columns} ===")
        
        # 检查是否有金额相关列
        amount_columns = [col for col in df.columns if '金额' in str(col) or '目标' in str(col) or '万元' in str(col)]
        if amount_columns:
            print(f"\n=== 包含金额/目标的列: {amount_columns} ===")
        
        return df
        
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None

if __name__ == "__main__":
    # Excel文件路径
    excel_file = "各公司债权清收目标.xlsx"
    
    # 读取并分析文件
    df = read_excel_file(excel_file)
    
    if df is not None:
        print("\n=== 文件读取成功 ===")
    else:
        print("\n=== 文件读取失败 ===")
