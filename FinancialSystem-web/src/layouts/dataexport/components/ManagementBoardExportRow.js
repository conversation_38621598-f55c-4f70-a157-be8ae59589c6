import React, { useState } from 'react';
import {
  Card,
  Grid,
  FormControl,
  Select,
  MenuItem,
  TextField,
  Snackbar,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
  Fade,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  LinearProgress,
  InputLabel,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  FileDownload as FileDownloadIcon,
  Preview as PreviewIcon,
  Close as CloseIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import StandardBarChart from 'components/charts/StandardBarChart';
import EnhancedGenericDataTable from 'components/tables/EnhancedGenericDataTable';

// 导出服务
import { exportManagementBoard } from '../services/exportService';

/**
 * 经营调度会看板导出行组件
 * 提供年份、月份、最小金额选择和一键导出功能
 */
const ManagementBoardExportRow = () => {
  const theme = useTheme();

  // 状态管理
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString());
  const [minAmount, setMinAmount] = useState('100');
  const [isExporting, setIsExporting] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 预览数据
  const [previewData] = useState([
    {
      id: 1,
      creditor: '上海金融资产管理有限公司',
      debtor: '北京科技发展有限公司',
      debtAmount: 850.5,
      debtBalance: 720.3,
      disposedPrincipal: 130.2,
      disposalRatio: '15.3%',
    },
    {
      id: 2,
      creditor: '深圳投资管理集团',
      debtor: '广州制造业有限公司',
      debtAmount: 1200.0,
      debtBalance: 980.5,
      disposedPrincipal: 219.5,
      disposalRatio: '18.3%',
    },
    {
      id: 3,
      creditor: '杭州资产运营公司',
      debtor: '南京贸易集团',
      debtAmount: 560.8,
      debtBalance: 450.6,
      disposedPrincipal: 110.2,
      disposalRatio: '19.6%',
    },
  ]);

  const previewColumns = [
    { Header: '债权人', accessor: 'creditor', width: '25%' },
    { Header: '债务人', accessor: 'debtor', width: '25%' },
    { Header: '债权金额(万元)', accessor: 'debtAmount', width: '15%' },
    { Header: '债权余额(万元)', accessor: 'debtBalance', width: '15%' },
    { Header: '已处置本金(万元)', accessor: 'disposedPrincipal', width: '10%' },
    { Header: '处置比例', accessor: 'disposalRatio', width: '10%' },
  ];

  const previewChartData = {
    labels: ['本金', '利息', '违约金', '其他'],
    datasets: [
      {
        label: '处置金额分布',
        data: [1250, 380, 120, 50],
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
        ],
      },
    ],
  };

  // 预览功能
  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const handlePreviewClose = () => {
    setPreviewOpen(false);
  };

  const handleExportFromPreview = () => {
    setPreviewOpen(false);
    handleExport();
  };

  // 生成年份选项（2020-2030）
  const yearOptions = [];
  for (let i = 2020; i <= 2030; i++) {
    yearOptions.push(i.toString());
  }

  // 月份选项
  const monthOptions = [
    { value: '1', label: '1月' },
    { value: '2', label: '2月' },
    { value: '3', label: '3月' },
    { value: '4', label: '4月' },
    { value: '5', label: '5月' },
    { value: '6', label: '6月' },
    { value: '7', label: '7月' },
    { value: '8', label: '8月' },
    { value: '9', label: '9月' },
    { value: '10', label: '10月' },
    { value: '11', label: '11月' },
    { value: '12', label: '12月' },
  ];

  // 处理导出
  const handleExport = async () => {
    if (isExporting) {
      return;
    }

    // 简单的参数验证
    if (!year || !month || !minAmount) {
      setNotification({
        open: true,
        message: '请完善所有参数后再导出',
        severity: 'warning',
      });
      return;
    }

    if (parseFloat(minAmount) < 0) {
      setNotification({
        open: true,
        message: '最小金额不能为负数',
        severity: 'warning',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const result = await exportManagementBoard(year, month, minAmount, progress => {
        setExportProgress(progress);
      });

      setNotification({
        open: true,
        message: `看板数据导出成功！文件名：${result.fileName}`,
        severity: 'success',
      });
    } catch (error) {
      console.error('导出失败:', error);
      setNotification({
        open: true,
        message: error.message || '导出失败，请重试',
        severity: 'error',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Card
        sx={{
          borderRadius: '16px',
          boxShadow: '0 2px 12px 0 rgba(0,0,0,.08)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          border: '1px solid rgba(0,0,0,0.05)',
          '&:hover': {
            boxShadow: '0 8px 25px 0 rgba(0,0,0,.15)',
            transform: 'translateY(-2px)',
          },
        }}
      >
        <MDBox p={4}>
          {/* 卡片头部 */}
          <MDBox display="flex" alignItems="center" mb={4}>
            <MDBox
              bgColor="info"
              variant="gradient"
              borderRadius="xl"
              shadow="md"
              p={2.5}
              mr={3}
              sx={{
                background: 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)',
              }}
            >
              <DashboardIcon sx={{ color: 'white', fontSize: '28px' }} />
            </MDBox>
            <MDBox>
              <MDTypography variant="h5" fontWeight="bold" color="dark">
                经营调度会看板导出
              </MDTypography>
              <MDTypography variant="body2" color="text" sx={{ mt: 0.5, opacity: 0.8 }}>
                导出逾期债权数据到经营调度会看板格式
              </MDTypography>
            </MDBox>
          </MDBox>

          {/* 参数选择区域 */}
          <Grid container spacing={3} alignItems="flex-end">
            {/* 年份选择 */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>年份</InputLabel>
                <Select
                  value={year}
                  onChange={e => setYear(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {yearOptions.map(year => (
                    <MenuItem key={year} value={year}>
                      {year}年
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 月份选择 */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>月份</InputLabel>
                <Select
                  value={month}
                  onChange={e => setMonth(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {monthOptions.map(month => (
                    <MenuItem key={month.value} value={month.value}>
                      {month.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 最小金额 */}
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                variant="outlined"
                label="最小金额 (万元)"
                type="number"
                value={minAmount}
                onChange={e => setMinAmount(e.target.value)}
                disabled={isExporting}
                inputProps={{ min: 0, step: 10 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    height: '56px',
                  },
                }}
              />
            </Grid>

            {/* 操作按钮 */}
            <Grid item xs={12} sm={6} md={3}>
              <MDBox display="flex" gap={1} height="56px" alignItems="center">
                <MDButton
                  variant="outlined"
                  color="info"
                  onClick={handlePreview}
                  disabled={isExporting}
                  sx={{ height: '40px', flex: 1 }}
                >
                  <PreviewIcon sx={{ mr: 1 }} />
                  预览
                </MDButton>
                <MDButton
                  variant="gradient"
                  color="info"
                  onClick={handleExport}
                  disabled={isExporting}
                  sx={{ height: '40px', flex: 1, minWidth: '80px' }}
                >
                  {isExporting ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : (
                    <>
                      <FileDownloadIcon sx={{ mr: 1, fontSize: '18px' }} />
                      导出
                    </>
                  )}
                </MDButton>
              </MDBox>
            </Grid>
          </Grid>

          {/* 说明文字 */}
          <MDBox mt={2} display="flex" alignItems="center">
            <BusinessIcon sx={{ color: 'info.main', mr: 1, fontSize: '16px' }} />
            <MDTypography variant="caption" color="text">
              此功能将导出符合条件的逾期债权数据，包含本金、利息、违约金等处置明细，适用于经营调度会汇报
            </MDTypography>
          </MDBox>
        </MDBox>
      </Card>

      {/* 导出进度指示器 */}
      {isExporting && (
        <MDBox
          position="fixed"
          top={0}
          left={0}
          width="100%"
          height="100%"
          bgcolor="rgba(0, 0, 0, 0.5)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          zIndex={9999}
        >
          <Card sx={{ p: 4, borderRadius: '12px', maxWidth: '400px' }}>
            <MDBox textAlign="center">
              <CircularProgress size={60} color="info" sx={{ mb: 2 }} />
              <MDTypography variant="h6" mb={1}>
                正在导出看板数据...
              </MDTypography>
              <MDTypography variant="body2" color="text">
                请稍候，正在处理您的导出请求
              </MDTypography>
              <LinearProgress
                variant="determinate"
                value={exportProgress}
                sx={{ mt: 2, height: '8px', borderRadius: '4px' }}
              />
              <MDTypography variant="caption" color="text" mt={1}>
                {exportProgress}% 完成
              </MDTypography>
            </MDBox>
          </Card>
        </MDBox>
      )}

      {/* 预览模态窗口 */}
      <Dialog
        open={previewOpen}
        onClose={handlePreviewClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px', minHeight: '70vh' },
        }}
      >
        <DialogTitle>
          <MDBox display="flex" alignItems="center" justifyContent="space-between">
            <MDBox display="flex" alignItems="center">
              <PreviewIcon sx={{ color: 'info.main', mr: 1 }} />
              <MDTypography variant="h6">经营调度会看板预览</MDTypography>
            </MDBox>
            <IconButton onClick={handlePreviewClose}>
              <CloseIcon />
            </IconButton>
          </MDBox>
        </DialogTitle>

        <DialogContent>
          <MDBox mb={2}>
            <StandardBarChart
              icon={{ component: 'bar_chart', color: 'info' }}
              title="处置金额分布"
              description="按类型统计的处置金额分布"
              chart={previewChartData}
              height="300px"
            />
          </MDBox>

          <EnhancedGenericDataTable
            table={{
              columns: previewColumns,
              rows: previewData,
            }}
            showToolbar={false}
            entriesPerPage={false}
            canSearch={true}
            showColumnsButton={true}
            showRefreshButton={false}
          />
        </DialogContent>

        <DialogActions>
          <MDButton variant="outlined" color="secondary" onClick={handlePreviewClose}>
            关闭
          </MDButton>
          <MDButton variant="gradient" color="info" onClick={handleExportFromPreview}>
            <FileDownloadIcon sx={{ mr: 1 }} />
            确认导出
          </MDButton>
        </DialogActions>
      </Dialog>

      {/* 通知组件 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        TransitionComponent={Fade}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 3,
            boxShadow: `0 8px 32px ${alpha(
              notification.severity === 'success'
                ? theme.palette.success.main
                : notification.severity === 'warning'
                  ? theme.palette.warning.main
                  : theme.palette.error.main,
              0.3,
            )}`,
            '& .MuiAlert-icon': {
              fontSize: '1.5rem',
            },
            '& .MuiAlert-message': {
              fontSize: '1rem',
              fontWeight: 600,
            },
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ManagementBoardExportRow;