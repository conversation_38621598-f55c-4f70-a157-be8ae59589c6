package com.laoshu198838.service;

import com.aspose.cells.*;
import com.laoshu198838.dto.report.OperationalDashboardDTO;
import com.laoshu198838.repository.overdue_debt.DebtDetailsExportRepository;
import com.laoshu198838.repository.overdue_debt.DebtDisposalActionRepository;
import com.laoshu198838.repository.overdue_debt.OverdueDebtDetailRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 经营调度会看板导出服务
 * 负责处理逾期债权数据导出到经营调度会看板的业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class ManagementBoardExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ManagementBoardExportService.class);
    
    @Autowired
    private OverdueDebtDetailRepository overdueDebtDetailRepository;
    
    @Autowired
    private DebtDisposalActionRepository debtDisposalActionRepository;
    
    @Autowired
    private DebtDetailsExportRepository debtDetailsExportRepository;
    
    /**
     * 导出经营调度会看板数据
     * 
     * @param year 年份
     * @param month 月份
     * @param minAmount 最小金额（万元）
     * @return Excel文件的字节数组响应
     */
    public ResponseEntity<byte[]> exportManagementBoardData(String year, String month, String minAmount) {
        try {
            logger.info("开始导出经营调度会看板数据 - 年份: {}, 月份: {}, 最小金额: {}万元", year, month, minAmount);
            
            // 1. 获取债权数据
            List<OperationalDashboardDTO> dashboardData = fetchDashboardData(year, month, minAmount);
            
            if (dashboardData.isEmpty()) {
                logger.warn("未找到符合条件的数据");
                return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"no_data.txt\"")
                    .contentType(MediaType.TEXT_PLAIN)
                    .body("未找到符合条件的数据".getBytes(StandardCharsets.UTF_8));
            }
            
            // 2. 创建新的Excel工作簿（暂时不使用模板，避免模板文件问题）
            Workbook workbook = new Workbook();
            Worksheet worksheet = workbook.getWorksheets().get(0);
            worksheet.setName("经营调度会看板");
            
            // 3. 填充数据到Excel
            fillExcelData(workbook, dashboardData, year, month);
            
            // 4. 导出为字节数组
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.save(byteArrayOutputStream, SaveFormat.XLSX);
            byte[] excelData = byteArrayOutputStream.toByteArray();
            
            // 5. 生成文件名
            String filename = String.format("经营调度会看板_%s年%s月.xlsx", year, month);
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
            
            logger.info("成功导出经营调度会看板数据，记录数: {}", dashboardData.size());
            
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFilename)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                .header(HttpHeaders.CACHE_CONTROL, "must-revalidate, post-check=0, pre-check=0")
                .header(HttpHeaders.PRAGMA, "public")
                .header(HttpHeaders.EXPIRES, "0")
                .body(excelData);
                
        } catch (Exception e) {
            logger.error("导出经营调度会看板数据失败", e);
            return ResponseEntity.internalServerError()
                .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 获取仪表板数据
     */
    private List<OperationalDashboardDTO> fetchDashboardData(String year, String month, String minAmount) {
        List<OperationalDashboardDTO> result = new ArrayList<>();
        
        try {
            // 转换最小金额为数值
            BigDecimal minAmountValue = new BigDecimal(minAmount).multiply(new BigDecimal("10000"));
            
            // 查询债权基础数据
            List<Map<String, Object>> debtList = overdueDebtDetailRepository.findDebtsByYearMonthAndMinAmount(
                year, month, minAmountValue
            );
            
            int sequenceNumber = 1;
            for (Map<String, Object> debt : debtList) {
                OperationalDashboardDTO dto = new OperationalDashboardDTO();
                dto.setSequenceNumber(sequenceNumber++);
                
                // 设置基础信息
                dto.setCreditor((String) debt.get("creditor"));
                dto.setDebtor((String) debt.get("debtor"));
                
                // 设置金额（转换为万元）
                BigDecimal debtAmount = toBigDecimal(debt.get("debt_amount"));
                BigDecimal debtBalance = toBigDecimal(debt.get("debt_balance"));
                BigDecimal cumulativeAmount = toBigDecimal(debt.get("cumulative_amount"));
                
                dto.setDebtAmount(debtAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                dto.setDebtBalance(debtBalance.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                
                // 如果有累计金额，使用累计金额
                if (cumulativeAmount.compareTo(BigDecimal.ZERO) > 0) {
                    dto.setDebtAmount(cumulativeAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                }
                
                // 获取处置金额明细
                Long debtId = toLong(debt.get("debt_id"));
                if (debtId != null) {
                    Map<String, BigDecimal> disposalDetails = getDisposalDetails(debtId, year, month);
                    
                    // 设置处置金额（转换为万元）
                    dto.setDisposedPrincipal(disposalDetails.get("principal").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedInterest(disposalDetails.get("interest").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedPenalty(disposalDetails.get("penalty").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                    dto.setDisposedOther(disposalDetails.get("other").divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP));
                }
                
                // 计算合计和处置比例
                dto.calculateTotalDisposedAmount();
                dto.calculateDisposalRatio();
                
                result.add(dto);
            }
            
        } catch (Exception e) {
            logger.error("获取仪表板数据失败", e);
        }
        
        return result;
    }
    
    /**
     * 获取债权的处置金额明细
     */
    private Map<String, BigDecimal> getDisposalDetails(Long debtId, String year, String month) {
        // 查询处置记录
        List<Map<String, Object>> disposalActions = debtDisposalActionRepository.findByDebtIdAndYearMonth(
            debtId, year, month
        );
        
        BigDecimal principal = BigDecimal.ZERO;
        BigDecimal interest = BigDecimal.ZERO;
        BigDecimal penalty = BigDecimal.ZERO;
        BigDecimal other = BigDecimal.ZERO;
        
        for (Map<String, Object> action : disposalActions) {
            // 获取各种处置方式的金额
            BigDecimal cashDisposal = toBigDecimal(action.get("cash_disposal"));
            BigDecimal installmentRepayment = toBigDecimal(action.get("installment_repayment"));
            BigDecimal assetDebt = toBigDecimal(action.get("asset_debt"));
            BigDecimal otherWays = toBigDecimal(action.get("other_ways"));
            
            // 现金处置和分期还款主要用于本金回收
            principal = principal.add(cashDisposal).add(installmentRepayment);
            
            // 资产抵债按比例分配：70%本金，20%利息，10%违约金
            if (assetDebt.compareTo(BigDecimal.ZERO) > 0) {
                principal = principal.add(assetDebt.multiply(new BigDecimal("0.70")));
                interest = interest.add(assetDebt.multiply(new BigDecimal("0.20")));
                penalty = penalty.add(assetDebt.multiply(new BigDecimal("0.10")));
            }
            
            // 其他方式按比例分配
            if (otherWays.compareTo(BigDecimal.ZERO) > 0) {
                Map<String, BigDecimal> splitAmounts = intelligentSplitAmount(debtId, otherWays);
                principal = principal.add(splitAmounts.get("principal"));
                interest = interest.add(splitAmounts.get("interest"));
                penalty = penalty.add(splitAmounts.get("penalty"));
                other = other.add(splitAmounts.get("other"));
            }
        }
        
        return Map.of(
            "principal", principal,
            "interest", interest,
            "penalty", penalty,
            "other", other
        );
    }
    
    /**
     * 智能拆分处置金额
     * 根据债权的历史数据和比例进行拆分
     */
    private Map<String, BigDecimal> intelligentSplitAmount(Long debtId, BigDecimal totalAmount) {
        // 默认拆分比例：本金70%，利息20%，违约金8%，其他2%
        BigDecimal principal = totalAmount.multiply(new BigDecimal("0.70"));
        BigDecimal interest = totalAmount.multiply(new BigDecimal("0.20"));
        BigDecimal penalty = totalAmount.multiply(new BigDecimal("0.08"));
        BigDecimal other = totalAmount.multiply(new BigDecimal("0.02"));
        
        // 可以根据实际业务规则调整拆分逻辑
        // 例如：查询历史拆分比例，或根据债权类型调整
        
        return Map.of(
            "principal", principal,
            "interest", interest,
            "penalty", penalty,
            "other", other
        );
    }
    
    /**
     * 填充Excel数据
     */
    private void fillExcelData(Workbook workbook, List<OperationalDashboardDTO> data, String year, String month) throws Exception {
        Worksheet worksheet = workbook.getWorksheets().get(0);
        Cells cells = worksheet.getCells();
        
        // 设置标题
        cells.get(0, 0).setValue(String.format("经营调度会看板 - %s年%s月", year, month));
        
        // 设置列宽
        for (int i = 0; i <= 10; i++) {
            worksheet.getCells().setColumnWidth(i, 15);
        }
        
        // 创建表头
        String[] headers = {
            "序号", "债权人", "债务人", "债权金额（万元）", "债权余额（万元）",
            "处置本金（万元）", "处置利息（万元）", "处置违约金（万元）", "处置其他（万元）",
            "处置合计（万元）", "处置比例"
        };
        
        // 设置表头样式
        Style headerStyle = cells.get(1, 0).getStyle();
        headerStyle.getFont().setBold(true);
        headerStyle.setBackgroundColor(com.aspose.cells.Color.getGray());
        headerStyle.setForegroundColor(com.aspose.cells.Color.getGray());
        headerStyle.setPattern(BackgroundType.SOLID);
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = cells.get(1, i);
            cell.setValue(headers[i]);
            cell.setStyle(headerStyle);
        }
        
        // 数据从第3行开始填充
        int rowIndex = 2;
        for (OperationalDashboardDTO dto : data) {
            // 填充数据
            cells.get(rowIndex, 0).setValue(dto.getSequenceNumber());
            cells.get(rowIndex, 1).setValue(dto.getCreditor());
            cells.get(rowIndex, 2).setValue(dto.getDebtor());
            cells.get(rowIndex, 3).setValue(dto.getDebtAmount().doubleValue());
            cells.get(rowIndex, 4).setValue(dto.getDebtBalance().doubleValue());
            cells.get(rowIndex, 5).setValue(dto.getDisposedPrincipal().doubleValue());
            cells.get(rowIndex, 6).setValue(dto.getDisposedInterest().doubleValue());
            cells.get(rowIndex, 7).setValue(dto.getDisposedPenalty().doubleValue());
            cells.get(rowIndex, 8).setValue(dto.getDisposedOther().doubleValue());
            cells.get(rowIndex, 9).setValue(dto.getTotalDisposedAmount().doubleValue());
            cells.get(rowIndex, 10).setValue(dto.getDisposalRatio().doubleValue() + "%");
            
            rowIndex++;
        }
        
        // 添加合计行
        int totalRowIndex = rowIndex;
        cells.get(totalRowIndex, 2).setValue("合计");
        
        // 设置合计公式
        String lastDataRow = String.valueOf(rowIndex);
        cells.get(totalRowIndex, 3).setFormula("SUM(D3:D" + lastDataRow + ")");
        cells.get(totalRowIndex, 4).setFormula("SUM(E3:E" + lastDataRow + ")");
        cells.get(totalRowIndex, 5).setFormula("SUM(F3:F" + lastDataRow + ")");
        cells.get(totalRowIndex, 6).setFormula("SUM(G3:G" + lastDataRow + ")");
        cells.get(totalRowIndex, 7).setFormula("SUM(H3:H" + lastDataRow + ")");
        cells.get(totalRowIndex, 8).setFormula("SUM(I3:I" + lastDataRow + ")");
        cells.get(totalRowIndex, 9).setFormula("SUM(J3:J" + lastDataRow + ")");
        
        // 合计行样式
        Style totalStyle = workbook.createStyle();
        totalStyle.getFont().setBold(true);
        for (int colIndex = 0; colIndex <= 10; colIndex++) {
            cells.get(totalRowIndex, colIndex).setStyle(totalStyle);
        }
    }
    
    // 工具方法
    private BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        return BigDecimal.ZERO;
    }
    
    private Long toLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }
}