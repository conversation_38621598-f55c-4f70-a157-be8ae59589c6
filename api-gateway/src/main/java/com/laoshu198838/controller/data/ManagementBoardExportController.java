package com.laoshu198838.controller.data;

import com.laoshu198838.service.ManagementBoardExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 经营调度会看板导出控制器
 * 提供逾期债权数据导出到经营调度会看板的功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Tag(name = "经营调度会看板导出", description = "提供逾期债权数据导出到经营调度会看板的功能")
@RestController
@RequestMapping("/api/export")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:5173", "http://localhost:8080"}, allowCredentials = "true")
public class ManagementBoardExportController {
    
    private static final Logger logger = LoggerFactory.getLogger(ManagementBoardExportController.class);
    
    @Autowired
    private ManagementBoardExportService managementBoardExportService;
    
    /**
     * 导出经营调度会看板数据
     * 需要ADMIN权限
     * 
     * @param year 年份，格式：YYYY
     * @param month 月份，格式：1-12
     * @param minAmount 最小金额限制（万元）
     * @return Excel文件的字节数组
     */
    @Operation(
        summary = "导出经营调度会看板数据",
        description = "导出指定年月的逾期债权数据到经营调度会看板格式，包含债权本金、利息、违约金等处置明细"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "导出成功，返回Excel文件"),
        @ApiResponse(responseCode = "401", description = "未授权，需要登录"),
        @ApiResponse(responseCode = "403", description = "权限不足，需要ADMIN权限"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/managementBoard")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<byte[]> exportManagementBoard(
            @Parameter(description = "年份，格式：YYYY", required = false, example = "2025")
            @RequestParam(required = false) String year,
            
            @Parameter(description = "月份，格式：1-12", required = false, example = "1")
            @RequestParam(required = false) String month,
            
            @Parameter(description = "最小金额限制（万元）", required = false, example = "100")
            @RequestParam(required = false, defaultValue = "0") String minAmount) {
        
        logger.info("收到经营调度会看板导出请求 - 年份: {}, 月份: {}, 最小金额: {}万元", year, month, minAmount);
        
        try {
            // 参数验证
            if (year == null || year.trim().isEmpty()) {
                year = String.valueOf(java.time.Year.now().getValue());
            }
            
            if (month == null || month.trim().isEmpty()) {
                month = String.valueOf(java.time.LocalDate.now().getMonthValue());
            }
            
            // 验证年份范围
            int yearInt = Integer.parseInt(year);
            if (yearInt < 2020 || yearInt > 2030) {
                logger.warn("年份参数超出范围: {}", year);
                return ResponseEntity.badRequest()
                    .body("年份必须在2020-2030之间".getBytes(java.nio.charset.StandardCharsets.UTF_8));
            }
            
            // 验证月份范围
            int monthInt = Integer.parseInt(month);
            if (monthInt < 1 || monthInt > 12) {
                logger.warn("月份参数无效: {}", month);
                return ResponseEntity.badRequest()
                    .body("月份必须在1-12之间".getBytes(java.nio.charset.StandardCharsets.UTF_8));
            }
            
            // 验证金额
            double minAmountDouble = Double.parseDouble(minAmount);
            if (minAmountDouble < 0) {
                logger.warn("金额参数无效: {}", minAmount);
                return ResponseEntity.badRequest()
                    .body("最小金额不能为负数".getBytes(java.nio.charset.StandardCharsets.UTF_8));
            }
            
            // 调用服务层导出数据
            return managementBoardExportService.exportManagementBoardData(year, month, minAmount);
            
        } catch (NumberFormatException e) {
            logger.error("参数格式错误", e);
            return ResponseEntity.badRequest()
                .body("参数格式错误，请检查输入".getBytes(java.nio.charset.StandardCharsets.UTF_8));
        } catch (Exception e) {
            logger.error("导出经营调度会看板数据失败", e);
            return ResponseEntity.internalServerError()
                .body(("导出失败: " + e.getMessage()).getBytes(java.nio.charset.StandardCharsets.UTF_8));
        }
    }
    
    /**
     * 测试接口：导出经营调度会看板数据（无需认证）
     * 仅用于开发测试，生产环境应删除
     */
    @Operation(
        summary = "测试接口：导出经营调度会看板数据",
        description = "测试用接口，无需认证。生产环境应删除此接口"
    )
    @GetMapping("/test/managementBoard")
    public ResponseEntity<byte[]> testExportManagementBoard(
            @RequestParam(required = false, defaultValue = "2025") String year,
            @RequestParam(required = false, defaultValue = "1") String month,
            @RequestParam(required = false, defaultValue = "100") String minAmount) {
        
        logger.warn("使用测试接口导出经营调度会看板数据 - 生产环境应删除此接口");
        return managementBoardExportService.exportManagementBoardData(year, month, minAmount);
    }
}